# 视频文字提取程序使用说明

## 程序概述

我已经为你创建了一个完整的视频文字提取系统，包含以下几个程序：

1. **opencv_extractor.py** - 视频帧提取程序（推荐使用）
2. **video_text_extractor.py** - 完整的OCR文字提取程序
3. **simple_extractor.py** - 简化版文字提取程序

## 🎯 推荐使用方案

由于PaddleOCR在当前环境中安装存在问题，建议使用**两步法**：

### 第一步：提取视频帧
```bash
python opencv_extractor.py
```

这个程序会：
- 自动扫描 `videos` 文件夹中的所有MP4视频
- 每2秒提取一帧图片
- 将提取的帧保存到 `extracted_frames` 文件夹
- 生成详细的帧信息文件

### 第二步：OCR文字识别
你可以选择以下任一方式处理提取的图片：

#### 方案A：在线OCR工具
- 使用百度OCR、腾讯OCR等在线服务
- 批量上传图片进行文字识别
- 手动整理识别结果

#### 方案B：本地OCR软件
- 使用天若OCR、PandaOCR等桌面软件
- 批量处理提取的图片
- 自动保存识别结果

#### 方案C：手动查看
- 直接查看提取的图片
- 手动记录包含文字的内容
- 适合文字较少的情况

## 📁 文件结构说明

运行程序后，你的文件夹结构如下：

```
视频生文字/
├── videos/                          # 原始视频文件夹
│   ├── 7544761815914319167/
│   │   └── video.mp4
│   ├── 7546606383500643618/
│   │   └── video.mp4
│   └── ...
├── extracted_frames/                # 提取的帧文件夹
│   └── video/                       # 每个视频对应一个子文件夹
│       ├── frame_0000_t0.00s.jpg   # 帧文件（带时间戳）
│       ├── frame_0001_t2.00s.jpg
│       ├── ...
│       └── frame_info.txt           # 帧信息文件
├── opencv_extractor.py              # 帧提取程序
├── video_text_extractor.py          # 完整OCR程序
├── config.py                        # 配置文件
├── requirements.txt                 # 依赖包列表
└── README.md                        # 详细文档
```

## ⚙️ 程序配置

### 修改帧提取间隔
如果你想调整提取帧的频率，可以修改 `opencv_extractor.py` 中的参数：

```python
# 在第12行附近找到这个函数
def extract_frames_from_video(video_path, output_folder, frame_interval=2):
```

将 `frame_interval=2` 改为其他值：
- `frame_interval=1` - 每1秒提取一帧（更密集）
- `frame_interval=5` - 每5秒提取一帧（更稀疏）

### 支持更多视频格式
程序目前支持MP4格式，如需支持其他格式，可以修改 `opencv_extractor.py` 中的第94行：

```python
video_files = list(video_folder.rglob("*.mp4"))
```

改为：
```python
# 支持多种格式
for ext in ["*.mp4", "*.avi", "*.mov", "*.mkv"]:
    video_files.extend(video_folder.rglob(ext))
```

## 📊 运行结果示例

程序成功运行后，你会看到类似的输出：

```
视频帧提取程序
==============================
找到 6 个视频文件

处理视频 1/6: video.mp4
视频信息:
  文件: videos\7546606383500643618\video.mp4
  FPS: 30.0
  总帧数: 45961
  时长: 1532.03秒
  帧提取间隔: 2秒
提取帧 1: frame_0000_t0.00s.jpg
提取帧 2: frame_0001_t2.00s.jpg
...

提取完成！
总共提取了 766 帧
帧文件保存在: extracted_frames\video
信息文件: extracted_frames\video\frame_info.txt
```

## 🔧 故障排除

### 问题1：程序运行没有输出
**解决方案**：检查videos文件夹是否存在且包含MP4文件

### 问题2：无法打开视频文件
**解决方案**：
1. 确认视频文件没有损坏
2. 尝试用其他播放器播放视频
3. 检查视频文件路径中是否包含特殊字符

### 问题3：提取的帧数量太多或太少
**解决方案**：调整 `frame_interval` 参数

### 问题4：想要完整的OCR功能
**解决方案**：
1. 手动安装PaddleOCR：`pip install paddleocr`
2. 运行：`python video_text_extractor.py`

## 📝 下一步建议

1. **批量处理图片**：使用在线OCR服务或桌面软件批量处理提取的帧
2. **结果整理**：将识别的文字按时间戳整理成文档
3. **优化提取**：根据视频内容调整帧提取间隔
4. **自动化**：如果需要经常处理视频，可以考虑编写批处理脚本

## 💡 使用技巧

1. **预览帧**：处理前先查看几张提取的帧，确认包含需要的文字
2. **选择性处理**：如果视频很长，可以只处理包含文字的时间段
3. **批量重命名**：使用文件管理器的批量重命名功能整理图片
4. **备份原文件**：处理前备份原始视频文件

---

如果你在使用过程中遇到任何问题，请随时询问！
