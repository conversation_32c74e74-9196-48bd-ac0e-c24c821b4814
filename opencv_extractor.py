# -*- coding: utf-8 -*-
"""
基于OpenCV的视频帧提取程序
先提取帧，然后用户可以选择其他OCR工具处理
"""

import os
import cv2
from pathlib import Path
from datetime import datetime

def extract_frames_from_video(video_path, output_folder, frame_interval=2):
    """从视频中提取帧并保存为图片"""

    # 创建输出文件夹
    output_path = Path(output_folder)
    output_path.mkdir(exist_ok=True)

    # 打开视频
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"错误: 无法打开视频文件 {video_path}")
        return False

    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = frame_count / fps if fps > 0 else 0

    print(f"视频信息:")
    print(f"  文件: {video_path}")
    print(f"  FPS: {fps}")
    print(f"  总帧数: {frame_count}")
    print(f"  时长: {duration:.2f}秒")
    print(f"  帧提取间隔: {frame_interval}秒")

    frame_step = int(fps * frame_interval)
    current_frame = 0
    extracted_count = 0

    # 创建信息文件
    info_file = output_path / "frame_info.txt"
    with open(info_file, 'w', encoding='utf-8') as f:
        f.write(f"视频帧提取信息\n")
        f.write(f"视频文件: {video_path}\n")
        f.write(f"提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"FPS: {fps}\n")
        f.write(f"总帧数: {frame_count}\n")
        f.write(f"时长: {duration:.2f}秒\n")
        f.write(f"帧提取间隔: {frame_interval}秒\n")
        f.write("=" * 50 + "\n\n")

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        if current_frame % frame_step == 0:
            timestamp = current_frame / fps

            # 保存帧
            frame_filename = f"frame_{extracted_count:04d}_t{timestamp:.2f}s.jpg"
            frame_path = output_path / frame_filename
            cv2.imwrite(str(frame_path), frame)

            # 记录信息
            with open(info_file, 'a', encoding='utf-8') as f:
                f.write(f"帧 {extracted_count:04d}: {frame_filename} (时间戳: {timestamp:.2f}秒)\n")

            extracted_count += 1
            print(f"提取帧 {extracted_count}: {frame_filename}")

        current_frame += 1

    cap.release()

    with open(info_file, 'a', encoding='utf-8') as f:
        f.write(f"\n提取完成！\n")
        f.write(f"总共提取了 {extracted_count} 帧\n")

    print(f"\n提取完成！")
    print(f"总共提取了 {extracted_count} 帧")
    print(f"帧文件保存在: {output_path}")
    print(f"信息文件: {info_file}")

    return True

def process_all_videos():
    """处理所有视频文件"""
    video_folder = Path("videos")
    if not video_folder.exists():
        print("videos文件夹不存在")
        return

    video_files = list(video_folder.rglob("*.mp4"))
    if not video_files:
        print("没有找到视频文件")
        return

    print(f"找到 {len(video_files)} 个视频文件")

    # 创建输出根目录
    output_root = Path("extracted_frames")
    output_root.mkdir(exist_ok=True)

    for i, video_path in enumerate(video_files, 1):
        print(f"\n处理视频 {i}/{len(video_files)}: {video_path.name}")

        # 为每个视频创建单独的输出文件夹
        video_output = output_root / video_path.stem

        success = extract_frames_from_video(video_path, video_output)
        if not success:
            print(f"处理视频 {video_path.name} 失败")

    print(f"\n所有视频处理完成！")
    print(f"帧文件保存在: {output_root}")
    print("\n接下来你可以:")
    print("1. 使用在线OCR工具处理提取的图片")
    print("2. 使用其他OCR软件批量处理")
    print("3. 手动查看图片并记录文字内容")

if __name__ == "__main__":
    print("视频帧提取程序")
    print("=" * 30)
    process_all_videos()
