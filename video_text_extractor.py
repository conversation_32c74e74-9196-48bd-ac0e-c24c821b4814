# -*- coding: utf-8 -*-
"""
视频文字提取程序
使用OpenCV提取视频帧，使用PaddleOCR识别文字
"""

import os
import cv2
import json
import csv
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import numpy as np
from tqdm import tqdm

try:
    from paddleocr import PaddleOCR
except ImportError:
    print("请先安装PaddleOCR: pip install paddleocr")
    exit(1)

from config import VIDEO_CONFIG, OCR_CONFIG, OUTPUT_CONFIG, LOG_CONFIG


class VideoTextExtractor:
    """视频文字提取器"""

    def __init__(self):
        """初始化提取器"""
        self.setup_logging()
        self.setup_ocr()
        self.setup_directories()

    def setup_logging(self):
        """设置日志"""
        log_level = getattr(logging, LOG_CONFIG['level'].upper())
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(LOG_CONFIG['log_file'], encoding='utf-8')
            ] if LOG_CONFIG['save_to_file'] else [logging.StreamHandler()]
        )
        self.logger = logging.getLogger(__name__)

    def setup_ocr(self):
        """初始化OCR引擎"""
        try:
            self.logger.info("正在初始化OCR引擎...")
            self.ocr = PaddleOCR(
                use_angle_cls=True,
                lang=OCR_CONFIG['lang'],
                use_gpu=OCR_CONFIG['use_gpu'],
                det_model_dir=OCR_CONFIG['det_model_dir'],
                rec_model_dir=OCR_CONFIG['rec_model_dir'],
                show_log=False
            )
            self.logger.info("OCR引擎初始化完成")
        except Exception as e:
            self.logger.error(f"OCR引擎初始化失败: {e}")
            raise

    def setup_directories(self):
        """创建必要的目录"""
        output_dir = Path(VIDEO_CONFIG['output_folder'])
        output_dir.mkdir(exist_ok=True)
        self.logger.info(f"输出目录: {output_dir.absolute()}")

    def get_video_files(self) -> List[Path]:
        """获取所有视频文件"""
        input_folder = Path(VIDEO_CONFIG['input_folder'])
        if not input_folder.exists():
            self.logger.error(f"输入文件夹不存在: {input_folder}")
            return []

        video_files = []
        supported_formats = VIDEO_CONFIG['supported_formats']

        # 递归搜索视频文件
        for file_path in input_folder.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in supported_formats:
                video_files.append(file_path)

        self.logger.info(f"找到 {len(video_files)} 个视频文件")
        return video_files

    def extract_frames(self, video_path: Path) -> List[Tuple[np.ndarray, float]]:
        """从视频中提取帧"""
        frames = []
        cap = cv2.VideoCapture(str(video_path))

        if not cap.isOpened():
            self.logger.error(f"无法打开视频文件: {video_path}")
            return frames

        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_interval = VIDEO_CONFIG['frame_interval']
        frame_step = int(fps * frame_interval)

        frame_count = 0
        extracted_count = 0

        self.logger.info(f"开始提取帧，FPS: {fps}, 间隔: {frame_interval}秒")

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            if frame_count % frame_step == 0:
                timestamp = frame_count / fps
                frames.append((frame, timestamp))
                extracted_count += 1

            frame_count += 1

        cap.release()
        self.logger.info(f"提取了 {extracted_count} 帧")
        return frames

    def ocr_frame(self, frame: np.ndarray) -> List[Dict]:
        """对单帧进行OCR识别"""
        try:
            results = self.ocr.ocr(frame, cls=True)
            if not results or not results[0]:
                return []

            extracted_texts = []
            for line in results[0]:
                if len(line) >= 2:
                    bbox, (text, confidence) = line[0], line[1]

                    # 过滤低置信度和短文本
                    if (confidence >= OCR_CONFIG['confidence_threshold'] and
                        len(text.strip()) >= OUTPUT_CONFIG['min_text_length']):

                        extracted_texts.append({
                            'text': text.strip(),
                            'confidence': confidence,
                            'bbox': bbox
                        })

            return extracted_texts

        except Exception as e:
            self.logger.error(f"OCR识别失败: {e}")
            return []

    def process_video(self, video_path: Path) -> Dict:
        """处理单个视频文件"""
        self.logger.info(f"开始处理视频: {video_path.name}")

        # 提取帧
        frames = self.extract_frames(video_path)
        if not frames:
            return {'video': video_path.name, 'texts': [], 'error': '无法提取帧'}

        # OCR识别
        all_texts = []
        for frame, timestamp in tqdm(frames, desc=f"OCR识别 {video_path.name}"):
            texts = self.ocr_frame(frame)
            for text_info in texts:
                text_info['timestamp'] = timestamp
                all_texts.append(text_info)

        # 去重处理
        if OUTPUT_CONFIG['remove_duplicates']:
            all_texts = self.remove_duplicates(all_texts)

        self.logger.info(f"视频 {video_path.name} 识别出 {len(all_texts)} 条文本")

        return {
            'video': video_path.name,
            'video_path': str(video_path),
            'texts': all_texts,
            'total_frames': len(frames),
            'total_texts': len(all_texts)
        }

    def remove_duplicates(self, texts: List[Dict]) -> List[Dict]:
        """去除重复文本"""
        seen_texts = set()
        unique_texts = []

        for text_info in texts:
            text = text_info['text']
            if text not in seen_texts:
                seen_texts.add(text)
                unique_texts.append(text_info)

        return unique_texts

    def save_results(self, results: List[Dict]):
        """保存结果到文件"""
        output_dir = Path(VIDEO_CONFIG['output_folder'])
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if OUTPUT_CONFIG['format'] == 'json':
            self.save_json(results, output_dir / f"extracted_texts_{timestamp}.json")
        elif OUTPUT_CONFIG['format'] == 'csv':
            self.save_csv(results, output_dir / f"extracted_texts_{timestamp}.csv")
        else:  # txt
            self.save_txt(results, output_dir / f"extracted_texts_{timestamp}.txt")

    def save_json(self, results: List[Dict], file_path: Path):
        """保存为JSON格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        self.logger.info(f"结果已保存到: {file_path}")

    def save_csv(self, results: List[Dict], file_path: Path):
        """保存为CSV格式"""
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # 写入表头
            headers = ['视频文件', '文本内容']
            if OUTPUT_CONFIG['include_timestamp']:
                headers.append('时间戳(秒)')
            if OUTPUT_CONFIG['include_confidence']:
                headers.append('置信度')
            writer.writerow(headers)

            # 写入数据
            for result in results:
                for text_info in result['texts']:
                    row = [result['video'], text_info['text']]
                    if OUTPUT_CONFIG['include_timestamp']:
                        row.append(f"{text_info['timestamp']:.2f}")
                    if OUTPUT_CONFIG['include_confidence']:
                        row.append(f"{text_info['confidence']:.3f}")
                    writer.writerow(row)

        self.logger.info(f"结果已保存到: {file_path}")

    def save_txt(self, results: List[Dict], file_path: Path):
        """保存为TXT格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            for result in results:
                f.write(f"=== 视频文件: {result['video']} ===\n")
                f.write(f"总帧数: {result['total_frames']}, 识别文本数: {result['total_texts']}\n\n")

                for text_info in result['texts']:
                    f.write(f"文本: {text_info['text']}\n")
                    if OUTPUT_CONFIG['include_timestamp']:
                        f.write(f"时间: {text_info['timestamp']:.2f}秒\n")
                    if OUTPUT_CONFIG['include_confidence']:
                        f.write(f"置信度: {text_info['confidence']:.3f}\n")
                    f.write("-" * 50 + "\n")

                f.write("\n" + "=" * 80 + "\n\n")

        self.logger.info(f"结果已保存到: {file_path}")

    def run(self):
        """运行主程序"""
        self.logger.info("开始视频文字提取任务")

        # 获取视频文件
        video_files = self.get_video_files()
        if not video_files:
            self.logger.warning("没有找到视频文件")
            return

        # 处理每个视频
        all_results = []
        for video_path in video_files:
            try:
                result = self.process_video(video_path)
                all_results.append(result)
            except Exception as e:
                self.logger.error(f"处理视频 {video_path.name} 时出错: {e}")

        # 保存结果
        if all_results:
            self.save_results(all_results)

            # 统计信息
            total_texts = sum(len(result['texts']) for result in all_results)
            self.logger.info(f"任务完成！共处理 {len(all_results)} 个视频，提取 {total_texts} 条文本")
        else:
            self.logger.warning("没有成功处理任何视频文件")


def main():
    """主函数"""
    try:
        extractor = VideoTextExtractor()
        extractor.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")


if __name__ == "__main__":
    main()
