# -*- coding: utf-8 -*-
"""
简化版视频文字提取程序
直接处理单个视频文件
"""

import os
import cv2
from pathlib import Path
from datetime import datetime

def extract_text_from_video(video_path, output_file):
    """从视频中提取文字"""

    # 写入开始信息
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"视频文字提取结果\n")
        f.write(f"视频文件: {video_path}\n")
        f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 50 + "\n\n")

    try:
        # 导入PaddleOCR
        from paddleocr import PaddleOCR
        ocr = PaddleOCR(use_angle_cls=True, lang='ch', show_log=False)

        # 打开视频
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            with open(output_file, 'a', encoding='utf-8') as f:
                f.write("错误: 无法打开视频文件\n")
            return False

        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_interval = 2  # 每2秒提取一帧
        frame_step = int(fps * frame_interval)

        frame_count = 0
        extracted_count = 0

        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"视频FPS: {fps}\n")
            f.write(f"帧提取间隔: {frame_interval}秒\n\n")

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            if frame_count % frame_step == 0:
                timestamp = frame_count / fps

                # OCR识别
                try:
                    results = ocr.ocr(frame, cls=True)
                    if results and results[0]:
                        with open(output_file, 'a', encoding='utf-8') as f:
                            f.write(f"时间戳: {timestamp:.2f}秒\n")
                            for line in results[0]:
                                if len(line) >= 2:
                                    text, confidence = line[1]
                                    if confidence >= 0.5 and len(text.strip()) >= 2:
                                        f.write(f"文本: {text.strip()} (置信度: {confidence:.3f})\n")
                            f.write("-" * 30 + "\n")
                except Exception as e:
                    with open(output_file, 'a', encoding='utf-8') as f:
                        f.write(f"OCR错误 (时间戳 {timestamp:.2f}秒): {e}\n")

                extracted_count += 1

            frame_count += 1

        cap.release()

        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"\n处理完成！\n")
            f.write(f"总帧数: {frame_count}\n")
            f.write(f"提取帧数: {extracted_count}\n")

        return True

    except Exception as e:
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"处理错误: {e}\n")
        return False

def main():
    """主函数"""
    # 查找第一个视频文件
    video_folder = Path("videos")
    video_files = list(video_folder.rglob("*.mp4"))

    if not video_files:
        print("没有找到视频文件")
        return

    video_path = video_files[0]
    output_file = f"extracted_text_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

    print(f"开始处理视频: {video_path}")
    print(f"输出文件: {output_file}")

    success = extract_text_from_video(video_path, output_file)

    if success:
        print("处理完成！")
    else:
        print("处理失败！")

if __name__ == "__main__":
    main()
