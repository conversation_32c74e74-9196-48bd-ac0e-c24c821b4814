# -*- coding: utf-8 -*-
"""
视频文字提取程序配置文件
"""

# 视频处理配置
VIDEO_CONFIG = {
    # 视频文件夹路径
    'input_folder': 'videos',

    # 输出结果文件夹
    'output_folder': 'extracted_texts',

    # 帧提取间隔（秒）- 每隔多少秒提取一帧
    'frame_interval': 2,

    # 支持的视频格式
    'supported_formats': ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv'],
}

# OCR配置
OCR_CONFIG = {
    # 使用的OCR语言，支持中英文
    'lang': 'ch',  # 'ch' for Chinese, 'en' for English

    # OCR置信度阈值，低于此值的识别结果将被过滤
    'confidence_threshold': 0.5,

    # 是否使用GPU加速（需要安装GPU版本的PaddlePaddle）
    'use_gpu': False,

    # 文字检测模型精度，可选：'mobile'（快速）, 'server'（精确）
    'det_model_dir': None,  # 使用默认模型
    'rec_model_dir': None,  # 使用默认模型
}

# 输出配置
OUTPUT_CONFIG = {
    # 输出格式：'txt', 'json', 'csv'
    'format': 'txt',

    # 是否保存带时间戳的结果
    'include_timestamp': True,

    # 是否保存置信度信息
    'include_confidence': True,

    # 文本去重
    'remove_duplicates': True,

    # 最小文本长度（字符数），短于此长度的文本将被过滤
    'min_text_length': 2,
}

# 日志配置
LOG_CONFIG = {
    # 日志级别：DEBUG, INFO, WARNING, ERROR
    'level': 'INFO',

    # 是否保存日志到文件
    'save_to_file': True,

    # 日志文件名
    'log_file': 'video_text_extraction.log',
}
