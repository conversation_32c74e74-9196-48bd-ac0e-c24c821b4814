# -*- coding: utf-8 -*-
"""
简化版视频文字提取测试程序
"""

import os
import cv2
from pathlib import Path

def test_opencv():
    """测试OpenCV"""
    print("测试OpenCV...")
    try:
        # 检查视频文件
        video_folder = Path("videos")
        if not video_folder.exists():
            print("videos文件夹不存在")
            return False

        video_files = list(video_folder.rglob("*.mp4"))
        print(f"找到 {len(video_files)} 个MP4文件")

        if video_files:
            video_path = video_files[0]
            print(f"测试视频: {video_path}")

            # 尝试打开视频
            cap = cv2.VideoCapture(str(video_path))
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                duration = frame_count / fps if fps > 0 else 0
                print(f"视频信息: FPS={fps}, 帧数={frame_count}, 时长={duration:.2f}秒")

                # 读取第一帧
                ret, frame = cap.read()
                if ret:
                    print(f"成功读取帧，尺寸: {frame.shape}")
                    return True
                else:
                    print("无法读取视频帧")
                    return False
            else:
                print("无法打开视频文件")
                return False
        else:
            print("没有找到视频文件")
            return False

    except Exception as e:
        print(f"OpenCV测试失败: {e}")
        return False

def test_paddleocr():
    """测试PaddleOCR"""
    print("\n测试PaddleOCR...")
    try:
        from paddleocr import PaddleOCR
        print("PaddleOCR导入成功")

        # 初始化OCR（使用最简单的配置）
        ocr = PaddleOCR(use_angle_cls=True, lang='ch', show_log=False)
        print("PaddleOCR初始化成功")
        return True

    except Exception as e:
        print(f"PaddleOCR测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始简化测试...")

    opencv_ok = test_opencv()
    paddleocr_ok = test_paddleocr()

    if opencv_ok and paddleocr_ok:
        print("\n✅ 所有组件测试通过，可以运行完整程序")
    else:
        print("\n❌ 部分组件测试失败，请检查安装")
