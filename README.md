# 视频文字提取程序

这是一个基于OpenCV和PaddleOCR的视频文字提取工具，可以自动从视频中提取文字信息。

## 功能特点

- 🎥 支持多种视频格式（MP4、AVI、MOV、MKV、FLV、WMV）
- 🔍 使用PaddleOCR进行高精度中英文OCR识别
- ⚙️ 可配置的帧提取间隔和OCR参数
- 📊 支持多种输出格式（TXT、JSON、CSV）
- 🚀 支持批量处理多个视频文件
- 📝 详细的日志记录和进度显示
- 🔧 灵活的配置选项

## 安装依赖

### 方法1：使用pip安装（推荐）

```bash
pip install -r requirements.txt
```

### 方法2：手动安装

```bash
pip install opencv-python==********
pip install paddlepaddle==2.5.2
pip install paddleocr==2.7.3
pip install Pillow==10.0.1
pip install numpy==1.24.3
pip install tqdm==4.66.1
```

## 使用方法

### 1. 准备视频文件

将需要提取文字的视频文件放在 `videos` 文件夹中。程序会自动递归搜索该文件夹下的所有支持格式的视频文件。

### 2. 配置参数（可选）

编辑 `config.py` 文件来调整程序参数：

- **视频处理配置**：帧提取间隔、支持格式等
- **OCR配置**：语言设置、置信度阈值、GPU加速等
- **输出配置**：输出格式、时间戳、置信度等
- **日志配置**：日志级别、文件保存等

### 3. 运行程序

```bash
python video_text_extractor.py
```

### 4. 查看结果

程序运行完成后，提取的文字结果会保存在 `extracted_texts` 文件夹中，文件名包含时间戳。

## 配置说明

### 视频处理配置

```python
VIDEO_CONFIG = {
    'input_folder': 'videos',           # 视频文件夹路径
    'output_folder': 'extracted_texts', # 输出结果文件夹
    'frame_interval': 2,                # 帧提取间隔（秒）
    'supported_formats': ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv'],
}
```

### OCR配置

```python
OCR_CONFIG = {
    'lang': 'ch',                    # 语言：'ch'中文，'en'英文
    'confidence_threshold': 0.5,     # 置信度阈值
    'use_gpu': False,               # 是否使用GPU加速
}
```

### 输出配置

```python
OUTPUT_CONFIG = {
    'format': 'txt',              # 输出格式：'txt', 'json', 'csv'
    'include_timestamp': True,     # 是否包含时间戳
    'include_confidence': True,    # 是否包含置信度
    'remove_duplicates': True,     # 是否去重
    'min_text_length': 2,         # 最小文本长度
}
```

## 输出格式说明

### TXT格式
纯文本格式，每个视频的结果分别显示，包含文本内容、时间戳和置信度。

### JSON格式
结构化数据格式，适合程序处理：
```json
[
  {
    "video": "video.mp4",
    "video_path": "videos/video.mp4",
    "texts": [
      {
        "text": "识别的文字",
        "confidence": 0.95,
        "timestamp": 2.5,
        "bbox": [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
      }
    ],
    "total_frames": 10,
    "total_texts": 5
  }
]
```

### CSV格式
表格格式，适合Excel等工具打开：
| 视频文件 | 文本内容 | 时间戳(秒) | 置信度 |
|---------|---------|-----------|--------|
| video.mp4 | 识别的文字 | 2.50 | 0.950 |

## 性能优化建议

1. **调整帧提取间隔**：增大 `frame_interval` 可以减少处理时间，但可能遗漏文字
2. **使用GPU加速**：如果有NVIDIA GPU，安装GPU版本的PaddlePaddle并设置 `use_gpu: True`
3. **调整置信度阈值**：提高 `confidence_threshold` 可以过滤低质量识别结果
4. **批量处理**：程序支持同时处理多个视频文件

## 常见问题

### Q: 程序运行很慢怎么办？
A: 可以尝试以下方法：
- 增大帧提取间隔（如设置为5秒）
- 启用GPU加速（需要GPU版本的PaddlePaddle）
- 减少视频分辨率

### Q: 识别准确率不高怎么办？
A: 可以尝试：
- 降低置信度阈值
- 检查视频质量和文字清晰度
- 调整OCR语言设置

### Q: 如何处理特定时间段的视频？
A: 可以先用视频编辑工具截取需要的片段，然后再进行文字提取。

## 技术原理

1. **视频帧提取**：使用OpenCV按指定间隔提取视频帧
2. **文字检测**：使用PaddleOCR的文字检测模型定位文字区域
3. **文字识别**：使用PaddleOCR的文字识别模型识别文字内容
4. **后处理**：去重、过滤、格式化输出

## 许可证

本项目仅供学习和研究使用。

## 更新日志

- v1.0.0: 初始版本，支持基本的视频文字提取功能
